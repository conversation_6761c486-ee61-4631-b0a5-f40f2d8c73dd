const Package = {
  FREE: "FREE",
  PRO: "PRO",
  TEAM_WORK: "TEAM_WORK",
};

const LimitBoard = {
  FREE: 5,
  PRO: 0,
  TEAM_WORK: 0,
};

const LimitMember = {
  FREE: 0,
  PRO: 0,
  TEAM_WORK: 20,
};

const LabelFeature = {
  FREE: 0,
  PRO: 1,
  TEAM_WORK: 1,
};

module.exports = (sequelize, DataTypes) => {
  const Package = sequelize.define(
    "Package",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      teamFeature: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      limitMember: {
        type: DataTypes.INTEGER,
        defaultValue: 5,
      },
      limitBoard: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      
    },
    {
      tableName: "packages",
      timestamps: false,
    }
  );

  Package.associate = (models) => {
    Package.hasMany(models.Workspace, {
      foreignKey: "package_id",
      as: "workspaces",
    });
  };

  return Package;
};
